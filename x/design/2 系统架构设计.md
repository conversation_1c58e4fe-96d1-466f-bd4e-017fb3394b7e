# Sinoair Agent 系统架构设计文档

## 1. 架构概述

### 1.1 项目背景
Sinoair Agent是一个基于LLM大模型的国际航空货运代理智能识别与回填系统。系统通过识别图片、PDF等文件内容，提取符合IATA规范的结构化数据，并通过自动化技术回写到目标网页系统中。

### 1.2 架构设计原则
- **模块化设计**：按业务功能划分模块，降低耦合度
- **分层架构**：采用经典的三层架构模式，职责清晰
- **微服务化**：支持服务拆分和独立部署
- **高可用性**：通过集群部署和故障转移保证系统稳定性
- **可扩展性**：支持水平扩展和功能插件化
- **安全性**：多重认证和权限控制机制

### 1.3 技术架构总览

```mermaid
graph TB
    subgraph "前端层"
        A1[管理员Web界面]
        A2[员工Web界面]
        A3[浏览器插件]
        A4[移动端应用]
    end

    subgraph "网关层"
        B1[API Gateway]
        B2[负载均衡器]
    end

    subgraph "应用服务层"
        C1[认证服务]
        C2[用户管理服务]
        C3[Agent管理服务]
        C4[文件处理服务]
        C5[回填服务]
        C6[日志服务]
    end

    subgraph "中间件层"
        D1[Redis缓存]
        D2[消息队列]
        D3[任务调度]
        D4[文件存储]
    end

    subgraph "数据层"
        E1[MySQL主库]
        E2[MySQL从库]
        E3[MongoDB]
        E4[Elasticsearch]
    end

    subgraph "外部服务"
        F1[LLM API服务]
        F2[对象存储OSS]
        F3[短信服务]
        F4[邮件服务]
    end

    A1 --> B1
    A2 --> B1
    A3 --> B1
    A4 --> B1

    B1 --> B2
    B2 --> C1
    B2 --> C2
    B2 --> C3
    B2 --> C4
    B2 --> C5
    B2 --> C6

    C1 --> D1
    C2 --> D1
    C3 --> D1
    C4 --> D2
    C5 --> D3
    C6 --> D1

    C1 --> E1
    C2 --> E1
    C3 --> E1
    C4 --> E3
    C5 --> E1
    C6 --> E4

    C4 --> F1
    C4 --> F2
    C2 --> F3
    C2 --> F4
```

## 2. 系统角色与权限架构

### 2.1 角色定义

```mermaid
graph LR
    A[Sinoair Agent 系统] --> B[系统管理员]
    A --> C[航空代理员工]

    B --> B1[用户管理]
    B --> B2[大模型管理]
    B --> B3[回填网站管理]
    B --> B4[日志查看]
    B --> B5[系统配置]
    B --> B6[监控告警]

    C --> C1[Agent管理]
    C --> C2[Agent使用]

    C1 --> C1.1[Agent创建和维护]
    C1 --> C1.2[Prompt调试]
    C1 --> C1.3[回填网站绑定]
    C1 --> C1.4[历史查看]

    C2 --> C2.1[文件上传处理]
    C2 --> C2.2[结果查看编辑]
    C2 --> C2.3[批量处理]
    C2 --> C2.4[回填操作]
```

### 2.2 权限控制架构

#### RBAC权限模型
```mermaid
erDiagram
    USER ||--o{ USER_ROLE : has
    ROLE ||--o{ USER_ROLE : assigned
    ROLE ||--o{ ROLE_PERMISSION : has
    PERMISSION ||--o{ ROLE_PERMISSION : granted
    RESOURCE ||--o{ PERMISSION : controls

    USER {
        int id
        string username
        string email
        string status
    }

    ROLE {
        int id
        string name
        string code
        string description
    }

    PERMISSION {
        int id
        string name
        string code
        string type
    }

    RESOURCE {
        int id
        string name
        string path
        string method
    }
```

## 3. 核心技术栈架构

### 3.1 后端技术栈

#### 3.1.1 基础框架层
```yaml
基础框架:
  语言: Java 17 (LTS版本)
  框架: Spring Boot 3.5.0
  安全: Spring Security 6.x
  核心: Spring Framework 6.x

特性:
  - 原生支持GraalVM
  - 响应式编程支持
  - 云原生特性
  - 性能优化
```

#### 3.1.2 数据访问层
```yaml
数据库:
  主数据库: MySQL 8.x
  文档数据库: MongoDB 5.x
  搜索引擎: Elasticsearch 8.x

连接管理:
  连接池: Druid 1.2.24
  ORM框架: MyBatis-Plus 3.5.5
  数据库驱动: mysql-connector-j 9.1.0

特性:
  - 读写分离
  - 分库分表
  - 数据加密
  - 审计日志
```

#### 3.1.3 缓存与存储层
```yaml
缓存系统:
  分布式缓存: Redis 7.x
  本地缓存: Caffeine 2.9.3
  分布式锁: Redisson 3.40.2

对象存储:
  私有云: MinIO 8.5.10
  公有云: 阿里云OSS 3.16.3

特性:
  - 多级缓存
  - 缓存预热
  - 缓存穿透防护
  - 分布式会话
```

#### 3.1.4 安全认证层
```yaml
认证授权:
  框架: Spring Security 6
  令牌: JWT + Redis
  验证码: Hutool Captcha
  多端登录: Redis会话管理

加密算法:
  密码加密: BCrypt
  数据加密: AES-256
  传输加密: TLS 1.3

特性:
  - 多因子认证
  - 单点登录
  - 会话管理
  - 权限缓存
```

### 3.2 前端技术栈

#### 3.2.1 Web前端架构
```yaml
框架技术:
  核心框架: Vue 3.x / React 18.x
  构建工具: Vite 4.x
  状态管理: Pinia / Redux Toolkit
  路由管理: Vue Router / React Router

UI组件:
  组件库: Element Plus / Ant Design
  图表库: ECharts / Chart.js
  表格组件: AG-Grid
  文件上传: Vue-Upload / React-Dropzone

开发工具:
  语言: TypeScript 5.x
  代码规范: ESLint + Prettier
  测试框架: Vitest / Jest
  包管理: pnpm
```

#### 3.2.2 移动端架构
```yaml
跨平台方案:
  框架: React Native / Flutter
  状态管理: Redux / Provider
  导航: React Navigation / Navigator

原生能力:
  文件选择: react-native-document-picker
  图片处理: react-native-image-picker
  网络请求: Axios
  本地存储: AsyncStorage
```

### 3.3 中间件技术栈

#### 3.3.1 消息队列
```yaml
消息中间件:
  主要选择: RabbitMQ 3.x
  备选方案: Apache Kafka

使用场景:
  - 异步文件处理
  - 批量任务处理
  - 系统解耦
  - 事件驱动

特性:
  - 消息持久化
  - 死信队列
  - 延迟队列
  - 消息确认
```

#### 3.3.2 任务调度
```yaml
调度框架:
  分布式任务: XXL-Job 2.4.2
  本地任务: Spring Task

任务类型:
  - 定时数据清理
  - 批量文件处理
  - 系统监控检查
  - 报表生成

特性:
  - 任务分片
  - 故障转移
  - 执行监控
  - 动态调度
```

## 4. 应用架构设计

### 4.1 分层架构

```mermaid
graph TB
    subgraph "表现层 (Presentation Layer)"
        A1[Controller层]
        A2[Web界面]
        A3[API接口]
        A4[异常处理]
    end

    subgraph "业务层 (Business Layer)"
        B1[Service层]
        B2[业务逻辑]
        B3[事务管理]
        B4[缓存管理]
    end

    subgraph "持久层 (Persistence Layer)"
        C1[Mapper层]
        C2[数据访问]
        C3[SQL映射]
        C4[连接管理]
    end

    subgraph "数据层 (Data Layer)"
        D1[MySQL数据库]
        D2[Redis缓存]
        D3[MongoDB文档]
        D4[文件存储]
    end

    A1 --> B1
    A2 --> A1
    A3 --> A1
    A4 --> A1

    B1 --> C1
    B2 --> B1
    B3 --> B1
    B4 --> B1

    C1 --> D1
    C2 --> C1
    C3 --> C1
    C4 --> C1

    D1 --> D4
    D2 --> D4
    D3 --> D4
```

### 4.2 包结构设计

#### 4.2.1 根包结构
```
com.sinoair.agent
├── SinoairAgentApplication.java        # 应用启动类
├── auth/                               # 认证模块
├── common/                             # 公共模块
├── config/                             # 配置模块
├── core/                               # 核心功能模块
├── module/                             # 业务模块
├── plugin/                             # 插件扩展模块
├── shared/                             # 共享服务模块
└── system/                             # 系统管理模块
```

#### 4.2.2 业务模块结构
```
module/
├── agent/                              # Agent管理模块
│   ├── controller/                     # 控制层
│   ├── service/                        # 业务层
│   ├── mapper/                         # 数据访问层
│   ├── model/                          # 数据模型
│   │   ├── entity/                     # 实体对象
│   │   ├── dto/                        # 数据传输对象
│   │   ├── vo/                         # 视图对象
│   │   └── form/                       # 表单对象
│   └── enums/                          # 枚举定义
├── processing/                         # 文件处理模块
├── fillback/                          # 回填模块
├── website/                           # 网站管理模块
└── llm/                               # LLM集成模块
```

## 5. 核心业务模块架构

### 5.1 Agent管理模块架构

```mermaid
graph TB
    subgraph "Agent管理模块"
        A1[Agent Controller]
        A2[Agent Service]
        A3[Prompt Service]
        A4[Version Service]
        A5[Test Service]
    end

    subgraph "数据层"
        B1[Agent Entity]
        B2[Prompt Entity]
        B3[Version Entity]
        B4[Test Record Entity]
    end

    subgraph "外部服务"
        C1[LLM API]
        C2[文件存储]
        C3[缓存服务]
    end

    A1 --> A2
    A1 --> A3
    A1 --> A4
    A1 --> A5

    A2 --> B1
    A3 --> B2
    A4 --> B3
    A5 --> B4

    A3 --> C1
    A2 --> C2
    A2 --> C3
```

#### Agent数据模型
```java
@Entity
@Table(name = "agents")
public class Agent extends BaseEntity {
    private String name;                    // Agent名称
    private String description;             // 描述
    private String category;                // 分类(AWB/发票/装箱单等)
    private String version;                 // 版本号
    private AgentStatus status;             // 状态
    private Long creatorId;                 // 创建者ID
    private String systemPrompt;            // 系统提示词
    private String userPromptTemplate;      // 用户提示词模板
    private String outputSchema;            // 输出格式定义
    private String validationRules;         // 验证规则
    private AgentConfig config;             // Agent配置
}
```

### 5.2 文件处理模块架构

```mermaid
graph LR
    subgraph "文件处理流程"
        A[文件上传] --> B[格式验证]
        B --> C[预处理]
        C --> D[LLM识别]
        D --> E[结果验证]
        E --> F[格式化输出]
        F --> G[结果存储]
    end

    subgraph "处理服务"
        H1[Upload Service]
        H2[Validation Service]
        H3[Processing Service]
        H4[LLM Service]
        H5[Result Service]
    end

    subgraph "存储层"
        I1[文件存储]
        I2[处理记录]
        I3[结果缓存]
    end

    A --> H1
    B --> H2
    C --> H3
    D --> H4
    E --> H5
    F --> H5
    G --> H5

    H1 --> I1
    H3 --> I2
    H5 --> I3
```

#### 处理记录数据模型
```java
@Entity
@Table(name = "processing_records")
public class ProcessingRecord extends BaseEntity {
    private Long userId;                    // 用户ID
    private Long agentId;                   // Agent ID
    private String fileName;                // 文件名
    private String filePath;                // 文件路径
    private Long fileSize;                  // 文件大小
    private String fileType;                // 文件类型
    private ProcessingStatus status;        // 处理状态
    private String inputData;               // 输入数据
    private String outputData;              // 输出数据
    private String errorMessage;            // 错误信息
    private Integer processingTime;         // 处理时间(毫秒)
    private Date completedAt;               // 完成时间
}
```

### 5.3 回填模块架构

```mermaid
graph TB
    subgraph "回填模块"
        A1[Fillback Controller]
        A2[Fillback Service]
        A3[Website Service]
        A4[Mapping Service]
        A5[Automation Service]
    end

    subgraph "自动化引擎"
        B1[Puppeteer Engine]
        B2[Playwright Engine]
        B3[Selenium Engine]
    end

    subgraph "配置管理"
        C1[Website Config]
        C2[Field Mapping]
        C3[Script Template]
    end

    A1 --> A2
    A2 --> A3
    A2 --> A4
    A2 --> A5

    A5 --> B1
    A5 --> B2
    A5 --> B3

    A3 --> C1
    A4 --> C2
    A5 --> C3
```

#### 网站配置数据模型
```java
@Entity
@Table(name = "websites")
public class Website extends BaseEntity {
    private String name;                    // 网站名称
    private String url;                     // 网站URL
    private String description;             // 描述
    private AuthType authType;              // 认证类型
    private String authConfig;              // 认证配置(JSON)
    private String selectors;               // 页面元素选择器(JSON)
    private WebsiteStatus status;           // 状态
    private String automationScript;        // 自动化脚本
}
```

## 6. 数据架构设计

### 6.1 数据库架构

#### 6.1.1 主数据库设计 (MySQL)
```mermaid
erDiagram
    USERS ||--o{ PROCESSING_RECORDS : creates
    USERS ||--o{ AGENTS : creates
    AGENTS ||--o{ PROCESSING_RECORDS : processes
    AGENTS ||--o{ AGENT_VERSIONS : has
    PROCESSING_RECORDS ||--o{ FILLBACK_RECORDS : generates
    WEBSITES ||--o{ FILLBACK_RECORDS : targets
    AGENTS ||--o{ FIELD_MAPPINGS : maps
    WEBSITES ||--o{ FIELD_MAPPINGS : maps

    USERS {
        bigint id PK
        varchar username UK
        varchar email UK
        varchar password_hash
        varchar role
        varchar department
        varchar status
        datetime created_at
        datetime updated_at
    }

    AGENTS {
        bigint id PK
        varchar name
        text description
        varchar category
        varchar version
        varchar status
        bigint creator_id FK
        text system_prompt
        text user_prompt_template
        json output_schema
        json validation_rules
        datetime created_at
        datetime updated_at
    }

    PROCESSING_RECORDS {
        bigint id PK
        bigint user_id FK
        bigint agent_id FK
        varchar file_name
        varchar file_path
        bigint file_size
        varchar file_type
        varchar status
        json input_data
        json output_data
        text error_message
        int processing_time
        datetime created_at
        datetime completed_at
    }

    WEBSITES {
        bigint id PK
        varchar name
        varchar url
        text description
        varchar auth_type
        json auth_config
        json selectors
        varchar status
        datetime created_at
        datetime updated_at
    }

    FILLBACK_RECORDS {
        bigint id PK
        bigint processing_record_id FK
        bigint website_id FK
        varchar status
        json fillback_data
        text error_message
        int retry_count
        datetime created_at
        datetime completed_at
    }
```

#### 6.1.2 文档数据库设计 (MongoDB)
```javascript
// Agent配置集合
{
  "_id": ObjectId,
  "agentId": NumberLong,
  "promptHistory": [
    {
      "version": "1.0.0",
      "systemPrompt": "...",
      "userPromptTemplate": "...",
      "outputSchema": {...},
      "createdAt": ISODate,
      "performance": {
        "accuracy": 0.95,
        "avgProcessingTime": 15000
      }
    }
  ],
  "testCases": [
    {
      "fileName": "test-awb.pdf",
      "expectedOutput": {...},
      "actualOutput": {...},
      "passed": true,
      "createdAt": ISODate
    }
  ]
}

// 处理日志集合
{
  "_id": ObjectId,
  "processingId": NumberLong,
  "steps": [
    {
      "step": "file_upload",
      "timestamp": ISODate,
      "duration": 1000,
      "status": "success",
      "details": {...}
    },
    {
      "step": "llm_processing",
      "timestamp": ISODate,
      "duration": 15000,
      "status": "success",
      "llmRequest": {...},
      "llmResponse": {...}
    }
  ]
}
```

### 6.2 缓存架构设计

#### 6.2.1 Redis缓存策略
```yaml
缓存分层:
  L1缓存: Caffeine本地缓存
    - 用户会话信息
    - 系统配置信息
    - 热点数据

  L2缓存: Redis分布式缓存
    - 用户认证Token
    - Agent配置信息
    - 处理结果缓存
    - 网站配置缓存

缓存Key设计:
  用户相关: "user:{userId}:*"
  Agent相关: "agent:{agentId}:*"
  处理相关: "processing:{recordId}:*"
  网站相关: "website:{websiteId}:*"

过期策略:
  用户Token: 24小时
  Agent配置: 1小时
  处理结果: 7天
  网站配置: 30分钟
```

#### 6.2.2 缓存更新策略
```mermaid
graph LR
    A[数据更新] --> B{缓存策略}
    B -->|写入| C[Cache Aside]
    B -->|更新| D[Write Through]
    B -->|删除| E[Cache Invalidation]

    C --> F[先更新DB]
    F --> G[删除缓存]

    D --> H[同时更新DB和缓存]

    E --> I[删除相关缓存]
    I --> J[下次访问重建]
```

## 7. 安全架构设计

### 7.1 认证架构

#### 7.1.1 多重认证机制
```mermaid
graph TB
    subgraph "认证方式"
        A1[用户名密码]
        A2[短信验证码]
        A3[邮箱验证]
        A4[微信登录]
        A5[LDAP集成]
    end

    subgraph "认证流程"
        B1[身份验证]
        B2[Token生成]
        B3[权限加载]
        B4[会话管理]
    end

    subgraph "安全控制"
        C1[登录限制]
        C2[设备绑定]
        C3[异地登录检测]
        C4[会话超时]
    end

    A1 --> B1
    A2 --> B1
    A3 --> B1
    A4 --> B1
    A5 --> B1

    B1 --> B2
    B2 --> B3
    B3 --> B4

    B4 --> C1
    B4 --> C2
    B4 --> C3
    B4 --> C4
```

#### 7.1.2 JWT Token设计
```java
public class JwtTokenProvider {

    // Token结构
    public class TokenClaims {
        private Long userId;                // 用户ID
        private String username;            // 用户名
        private String role;                // 角色
        private List<String> permissions;   // 权限列表
        private String deviceId;            // 设备ID
        private Long issuedAt;              // 签发时间
        private Long expiresAt;             // 过期时间
    }

    // Token生成
    public String generateToken(User user, String deviceId) {
        return Jwts.builder()
            .setSubject(user.getUsername())
            .claim("userId", user.getId())
            .claim("role", user.getRole())
            .claim("permissions", user.getPermissions())
            .claim("deviceId", deviceId)
            .setIssuedAt(new Date())
            .setExpiration(new Date(System.currentTimeMillis() + expiration))
            .signWith(SignatureAlgorithm.HS512, secret)
            .compact();
    }
}
```

### 7.2 授权架构

#### 7.2.1 权限控制模型
```mermaid
graph TB
    subgraph "权限层级"
        A1[系统权限]
        A2[模块权限]
        A3[功能权限]
        A4[数据权限]
    end

    subgraph "权限类型"
        B1[菜单权限]
        B2[按钮权限]
        B3[API权限]
        B4[字段权限]
    end

    subgraph "权限控制"
        C1[角色权限]
        C2[用户权限]
        C3[部门权限]
        C4[数据范围]
    end

    A1 --> A2
    A2 --> A3
    A3 --> A4

    B1 --> C1
    B2 --> C1
    B3 --> C2
    B4 --> C3

    C1 --> C4
    C2 --> C4
    C3 --> C4
```

#### 7.2.2 权限注解设计
```java
// 方法级权限控制
@PreAuthorize("hasRole('ADMIN') or hasPermission(#agentId, 'AGENT', 'READ')")
public Agent getAgent(@PathVariable Long agentId) {
    return agentService.getById(agentId);
}

// 数据权限控制
@DataScope(deptAlias = "d", userAlias = "u")
public List<ProcessingRecord> getProcessingRecords(ProcessingQuery query) {
    return processingService.list(query);
}

// API访问控制
@RateLimiter(key = "processing", rate = 10, interval = 60)
@RequiresPermissions("processing:upload")
public Result<String> uploadFile(@RequestParam MultipartFile file) {
    return processingService.uploadFile(file);
}
```

### 7.3 数据安全架构

#### 7.3.1 数据加密策略
```yaml
传输加密:
  协议: TLS 1.3
  证书: SSL证书
  算法: ECDHE-RSA-AES256-GCM-SHA384

存储加密:
  数据库: AES-256-CBC
  文件: AES-256-GCM
  配置: Jasypt加密

字段加密:
  敏感字段: @Encrypted注解
  加密算法: AES-256
  密钥管理: KMS密钥管理服务
```

#### 7.3.2 数据脱敏策略
```java
@Component
public class DataMaskingService {

    // 手机号脱敏
    @DataMasking(type = MaskingType.MOBILE)
    private String mobile;

    // 邮箱脱敏
    @DataMasking(type = MaskingType.EMAIL)
    private String email;

    // 身份证脱敏
    @DataMasking(type = MaskingType.ID_CARD)
    private String idCard;

    // 自定义脱敏规则
    @DataMasking(pattern = "(?<=.{2}).*(?=.{2})")
    private String customField;
}
```

## 8. 集成架构设计

### 8.1 LLM集成架构

#### 8.1.1 多模型支持架构
```mermaid
graph TB
    subgraph "LLM适配层"
        A1[LLM Service Interface]
        A2[OpenAI Adapter]
        A3[Claude Adapter]
        A4[文心一言 Adapter]
        A5[通义千问 Adapter]
    end

    subgraph "模型管理"
        B1[Model Registry]
        B2[Model Router]
        B3[Load Balancer]
        B4[Fallback Handler]
    end

    subgraph "请求处理"
        C1[Request Builder]
        C2[Response Parser]
        C3[Error Handler]
        C4[Retry Manager]
    end

    A1 --> A2
    A1 --> A3
    A1 --> A4
    A1 --> A5

    A2 --> B1
    A3 --> B1
    A4 --> B1
    A5 --> B1

    B1 --> B2
    B2 --> B3
    B3 --> B4

    B2 --> C1
    C1 --> C2
    C2 --> C3
    C3 --> C4
```

#### 8.1.2 LLM服务接口设计
```java
public interface LLMService {

    /**
     * 处理文档识别请求
     */
    LLMResponse processDocument(LLMRequest request);

    /**
     * 批量处理文档
     */
    List<LLMResponse> batchProcessDocuments(List<LLMRequest> requests);

    /**
     * 流式处理
     */
    Flux<LLMResponse> streamProcess(LLMRequest request);
}

@Component
public class LLMServiceImpl implements LLMService {

    @Autowired
    private ModelRouter modelRouter;

    @Autowired
    private RetryTemplate retryTemplate;

    @Override
    public LLMResponse processDocument(LLMRequest request) {
        return retryTemplate.execute(context -> {
            LLMAdapter adapter = modelRouter.route(request);
            return adapter.process(request);
        });
    }
}
```

### 8.2 自动化集成架构

#### 8.2.1 浏览器自动化架构
```mermaid
graph LR
    subgraph "自动化引擎"
        A1[Automation Manager]
        A2[Puppeteer Engine]
        A3[Playwright Engine]
        A4[Selenium Engine]
    end

    subgraph "脚本管理"
        B1[Script Repository]
        B2[Script Executor]
        B3[Script Validator]
        B4[Script Generator]
    end

    subgraph "浏览器管理"
        C1[Browser Pool]
        C2[Session Manager]
        C3[Resource Monitor]
        C4[Cleanup Service]
    end

    A1 --> A2
    A1 --> A3
    A1 --> A4

    A2 --> B1
    A3 --> B1
    A4 --> B1

    B1 --> B2
    B2 --> B3
    B3 --> B4

    B2 --> C1
    C1 --> C2
    C2 --> C3
    C3 --> C4
```

#### 8.2.2 自动化脚本模板
```javascript
// Puppeteer脚本模板
class FillbackScript {

    async execute(page, data, config) {
        try {
            // 1. 导航到目标页面
            await page.goto(config.url, { waitUntil: 'networkidle2' });

            // 2. 执行登录
            if (config.authRequired) {
                await this.login(page, config.auth);
            }

            // 3. 填写表单数据
            await this.fillForm(page, data, config.selectors);

            // 4. 提交表单
            await this.submitForm(page, config.submitSelector);

            // 5. 验证结果
            return await this.validateResult(page, config.successSelector);

        } catch (error) {
            throw new FillbackException(`脚本执行失败: ${error.message}`);
        }
    }

    async fillForm(page, data, selectors) {
        for (const [field, selector] of Object.entries(selectors)) {
            if (data[field]) {
                await page.waitForSelector(selector);
                await page.type(selector, data[field]);
            }
        }
    }
}
```

### 8.3 第三方服务集成

#### 8.3.1 对象存储集成
```java
@Component
public class FileStorageService {

    @Autowired
    private MinIOClient minioClient;

    @Autowired
    private OSSClient ossClient;

    /**
     * 上传文件
     */
    public String uploadFile(MultipartFile file, String bucket) {
        try {
            String fileName = generateFileName(file.getOriginalFilename());

            // 根据配置选择存储方式
            if (storageConfig.getType().equals("minio")) {
                return uploadToMinio(file, bucket, fileName);
            } else {
                return uploadToOSS(file, bucket, fileName);
            }
        } catch (Exception e) {
            throw new FileUploadException("文件上传失败", e);
        }
    }

    /**
     * 下载文件
     */
    public InputStream downloadFile(String bucket, String fileName) {
        try {
            if (storageConfig.getType().equals("minio")) {
                return minioClient.getObject(
                    GetObjectArgs.builder()
                        .bucket(bucket)
                        .object(fileName)
                        .build()
                );
            } else {
                return ossClient.getObject(bucket, fileName).getObjectContent();
            }
        } catch (Exception e) {
            throw new FileDownloadException("文件下载失败", e);
        }
    }
}
```

## 9. 部署架构设计

### 9.1 容器化架构

#### 9.1.1 Docker容器架构
```mermaid
graph TB
    subgraph "容器编排"
        A1[Docker Compose]
        A2[Kubernetes]
        A3[Docker Swarm]
    end

    subgraph "应用容器"
        B1[Frontend Container]
        B2[Backend Container]
        B3[Gateway Container]
        B4[Worker Container]
    end

    subgraph "中间件容器"
        C1[MySQL Container]
        C2[Redis Container]
        C3[MongoDB Container]
        C4[RabbitMQ Container]
    end

    subgraph "监控容器"
        D1[Prometheus Container]
        D2[Grafana Container]
        D3[ELK Container]
    end

    A1 --> B1
    A1 --> B2
    A1 --> B3
    A1 --> B4

    A1 --> C1
    A1 --> C2
    A1 --> C3
    A1 --> C4

    A1 --> D1
    A1 --> D2
    A1 --> D3
```

#### 9.1.2 Docker Compose配置
```yaml
version: '3.8'

services:
  # 前端服务
  frontend:
    build: ./frontend
    ports:
      - "80:80"
    depends_on:
      - gateway
    networks:
      - sinoair-network

  # API网关
  gateway:
    build: ./gateway
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
    depends_on:
      - backend
      - redis
    networks:
      - sinoair-network

  # 后端服务
  backend:
    build: ./backend
    ports:
      - "8081:8081"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - MYSQL_HOST=mysql
      - REDIS_HOST=redis
      - MONGODB_HOST=mongodb
    depends_on:
      - mysql
      - redis
      - mongodb
    networks:
      - sinoair-network

  # 文件处理服务
  processor:
    build: ./processor
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - RABBITMQ_HOST=rabbitmq
    depends_on:
      - rabbitmq
      - mongodb
    networks:
      - sinoair-network

  # MySQL数据库
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: sinoair_agent
      MYSQL_USER: sinoair
      MYSQL_PASSWORD: sinoair123
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql:/docker-entrypoint-initdb.d
    networks:
      - sinoair-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - sinoair-network

  # MongoDB文档数据库
  mongodb:
    image: mongo:6
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: admin123
    volumes:
      - mongodb_data:/data/db
    networks:
      - sinoair-network

  # RabbitMQ消息队列
  rabbitmq:
    image: rabbitmq:3-management
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: admin123
    ports:
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - sinoair-network

volumes:
  mysql_data:
  redis_data:
  mongodb_data:
  rabbitmq_data:

networks:
  sinoair-network:
    driver: bridge
```

### 9.2 云原生架构

#### 9.2.1 Kubernetes部署架构
```yaml
# Namespace
apiVersion: v1
kind: Namespace
metadata:
  name: sinoair-agent

---
# ConfigMap
apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
  namespace: sinoair-agent
data:
  application.yml: |
    spring:
      profiles:
        active: k8s
      datasource:
        url: *********************************************
        username: sinoair
        password: sinoair123
      redis:
        host: redis-service
        port: 6379

---
# Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend-deployment
  namespace: sinoair-agent
spec:
  replicas: 3
  selector:
    matchLabels:
      app: backend
  template:
    metadata:
      labels:
        app: backend
    spec:
      containers:
      - name: backend
        image: sinoair/agent-backend:latest
        ports:
        - containerPort: 8081
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "k8s"
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
      volumes:
      - name: config-volume
        configMap:
          name: app-config

---
# Service
apiVersion: v1
kind: Service
metadata:
  name: backend-service
  namespace: sinoair-agent
spec:
  selector:
    app: backend
  ports:
  - protocol: TCP
    port: 8081
    targetPort: 8081
  type: ClusterIP

---
# Ingress
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: app-ingress
  namespace: sinoair-agent
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  rules:
  - host: sinoair-agent.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: backend-service
            port:
              number: 8081
```

### 9.3 监控架构

#### 9.3.1 监控体系架构
```mermaid
graph TB
    subgraph "数据采集层"
        A1[应用指标]
        A2[系统指标]
        A3[业务指标]
        A4[日志数据]
    end

    subgraph "数据处理层"
        B1[Prometheus]
        B2[Elasticsearch]
        B3[Logstash]
        B4[Fluentd]
    end

    subgraph "存储层"
        C1[时序数据库]
        C2[日志存储]
        C3[指标存储]
    end

    subgraph "展示层"
        D1[Grafana]
        D2[Kibana]
        D3[自定义Dashboard]
    end

    subgraph "告警层"
        E1[AlertManager]
        E2[钉钉告警]
        E3[邮件告警]
        E4[短信告警]
    end

    A1 --> B1
    A2 --> B1
    A3 --> B1
    A4 --> B3

    B1 --> C1
    B2 --> C2
    B3 --> C2
    B4 --> C2

    C1 --> D1
    C2 --> D2
    C1 --> D3

    B1 --> E1
    E1 --> E2
    E1 --> E3
    E1 --> E4
```

#### 9.3.2 监控指标设计
```yaml
系统指标:
  - CPU使用率
  - 内存使用率
  - 磁盘使用率
  - 网络IO
  - 文件句柄数

应用指标:
  - QPS (每秒请求数)
  - 响应时间
  - 错误率
  - 活跃连接数
  - 线程池状态

业务指标:
  - 文件处理数量
  - Agent调用次数
  - 回填成功率
  - 用户活跃度
  - 系统容量使用率

告警规则:
  - CPU使用率 > 80% 持续5分钟
  - 内存使用率 > 85% 持续3分钟
  - 错误率 > 5% 持续2分钟
  - 响应时间 > 3秒 持续1分钟
  - 回填成功率 < 90% 持续10分钟
```

## 10. 性能架构设计

### 10.1 性能优化策略

#### 10.1.1 应用层优化
```java
@Configuration
@EnableAsync
public class PerformanceConfig {

    // 异步任务线程池
    @Bean("taskExecutor")
    public ThreadPoolTaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(50);
        executor.setQueueCapacity(200);
        executor.setThreadNamePrefix("async-task-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }

    // 文件处理线程池
    @Bean("fileProcessorExecutor")
    public ThreadPoolTaskExecutor fileProcessorExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("file-processor-");
        return executor;
    }
}

// 异步文件处理
@Service
public class AsyncProcessingService {

    @Async("fileProcessorExecutor")
    public CompletableFuture<ProcessingResult> processFileAsync(ProcessingRequest request) {
        try {
            ProcessingResult result = processFile(request);
            return CompletableFuture.completedFuture(result);
        } catch (Exception e) {
            CompletableFuture<ProcessingResult> future = new CompletableFuture<>();
            future.completeExceptionally(e);
            return future;
        }
    }
}
```

#### 10.1.2 数据库优化
```sql
-- 索引优化
CREATE INDEX idx_processing_records_user_created ON processing_records(user_id, created_at);
CREATE INDEX idx_processing_records_agent_status ON processing_records(agent_id, status);
CREATE INDEX idx_fillback_records_status_created ON fillback_records(status, created_at);

-- 分区表设计
CREATE TABLE processing_records_2024 (
    LIKE processing_records INCLUDING ALL
) PARTITION BY RANGE (EXTRACT(YEAR FROM created_at));

CREATE TABLE processing_records_2024_q1 PARTITION OF processing_records_2024
    FOR VALUES FROM ('2024-01-01') TO ('2024-04-01');

-- 读写分离配置
@Configuration
public class DataSourceConfig {

    @Bean
    @Primary
    public DataSource masterDataSource() {
        return DataSourceBuilder.create()
            .url("*****************************************")
            .username("sinoair")
            .password("sinoair123")
            .build();
    }

    @Bean
    public DataSource slaveDataSource() {
        return DataSourceBuilder.create()
            .url("****************************************")
            .username("sinoair")
            .password("sinoair123")
            .build();
    }
}
```

### 10.2 扩展性架构

#### 10.2.1 水平扩展设计
```mermaid
graph TB
    subgraph "负载均衡层"
        A1[Nginx]
        A2[HAProxy]
        A3[云负载均衡]
    end

    subgraph "应用集群"
        B1[App Instance 1]
        B2[App Instance 2]
        B3[App Instance 3]
        B4[App Instance N]
    end

    subgraph "数据库集群"
        C1[Master DB]
        C2[Slave DB 1]
        C3[Slave DB 2]
    end

    subgraph "缓存集群"
        D1[Redis Master]
        D2[Redis Slave 1]
        D3[Redis Slave 2]
    end

    A1 --> B1
    A1 --> B2
    A1 --> B3
    A1 --> B4

    B1 --> C1
    B2 --> C2
    B3 --> C3
    B4 --> C1

    B1 --> D1
    B2 --> D2
    B3 --> D3
    B4 --> D1
```

#### 10.2.2 自动扩缩容配置
```yaml
# Kubernetes HPA配置
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: backend-hpa
  namespace: sinoair-agent
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: backend-deployment
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 60
```

## 11. 总结

### 11.1 架构特点总结

1. **模块化设计**：采用清晰的模块划分，降低系统耦合度
2. **分层架构**：经典的三层架构，职责分明，易于维护
3. **微服务化**：支持服务拆分，便于独立部署和扩展
4. **云原生**：容器化部署，支持Kubernetes编排
5. **高可用**：集群部署，故障转移，保证系统稳定性
6. **高性能**：多级缓存，异步处理，数据库优化
7. **安全性**：多重认证，权限控制，数据加密
8. **可观测性**：完善的监控告警体系

### 11.2 技术选型优势

1. **现代化技术栈**：Java 17 + Spring Boot 3，性能和开发效率并重
2. **成熟的生态**：丰富的第三方组件，降低开发成本
3. **企业级特性**：安全、稳定、可扩展，满足企业级应用需求
4. **开发友好**：完善的开发工具链，提升开发效率

### 11.3 实施建议

1. **分阶段实施**：按模块逐步实施，降低风险
2. **持续集成**：建立CI/CD流水线，提升交付效率
3. **监控先行**：优先建立监控体系，保证系统可观测性
4. **安全优先**：从设计阶段就考虑安全因素
5. **性能测试**：定期进行性能测试，确保系统性能指标

本架构设计为Sinoair Agent系统提供了完整的技术架构指导，涵盖了从应用架构到部署架构的各个方面，为系统的成功实施奠定了坚实的基础。
```