# Sinoair Agent 系统架构设计文档

## 1. 架构概述

### 1.1 项目背景
Sinoair Agent是一个基于LLM大模型的国际航空货运代理智能识别与回填系统。系统通过识别图片、PDF等文件内容，提取符合IATA规范的结构化数据，并通过自动化技术回写到目标网页系统中。

### 1.2 架构设计原则
- **模块化设计**：按业务功能划分模块，降低耦合度
- **分层架构**：采用经典的三层架构模式，职责清晰
- **微服务化**：支持服务拆分和独立部署
- **高可用性**：通过集群部署和故障转移保证系统稳定性
- **可扩展性**：支持水平扩展和功能插件化
- **安全性**：多重认证和权限控制机制

### 1.3 技术架构总览

```mermaid
graph TB
    subgraph "前端层"
        A1[管理员Web界面]
        A2[员工Web界面]
        A3[浏览器插件]
        A4[移动端应用]
    end

    subgraph "网关层"
        B1[API Gateway]
        B2[负载均衡器]
    end

    subgraph "应用服务层"
        C1[认证服务]
        C2[用户管理服务]
        C3[Agent管理服务]
        C4[文件处理服务]
        C5[回填服务]
        C6[日志服务]
    end

    subgraph "中间件层"
        D1[Redis缓存]
        D2[消息队列]
        D3[任务调度]
        D4[文件存储]
    end

    subgraph "数据层"
        E1[MySQL主库]
        E2[MySQL从库]
        E3[MongoDB]
        E4[Elasticsearch]
    end

    subgraph "外部服务"
        F1[LLM API服务]
        F2[对象存储OSS]
        F3[短信服务]
        F4[邮件服务]
    end

    A1 --> B1
    A2 --> B1
    A3 --> B1
    A4 --> B1

    B1 --> B2
    B2 --> C1
    B2 --> C2
    B2 --> C3
    B2 --> C4
    B2 --> C5
    B2 --> C6

    C1 --> D1
    C2 --> D1
    C3 --> D1
    C4 --> D2
    C5 --> D3
    C6 --> D1

    C1 --> E1
    C2 --> E1
    C3 --> E1
    C4 --> E3
    C5 --> E1
    C6 --> E4

    C4 --> F1
    C4 --> F2
    C2 --> F3
    C2 --> F4
```

## 2. 系统角色与权限架构

### 2.1 角色定义

```mermaid
graph LR
    A[Sinoair Agent 系统] --> B[系统管理员]
    A --> C[航空代理员工]

    B --> B1[用户管理]
    B --> B2[大模型管理]
    B --> B3[回填网站管理]
    B --> B4[日志查看]
    B --> B5[系统配置]
    B --> B6[监控告警]

    C --> C1[Agent管理]
    C --> C2[Agent使用]

    C1 --> C1.1[Agent创建和维护]
    C1 --> C1.2[Prompt调试]
    C1 --> C1.3[回填网站绑定]
    C1 --> C1.4[历史查看]

    C2 --> C2.1[文件上传处理]
    C2 --> C2.2[结果查看编辑]
    C2 --> C2.3[批量处理]
    C2 --> C2.4[回填操作]
```

### 2.2 权限控制架构

#### RBAC权限模型
```mermaid
erDiagram
    USER ||--o{ USER_ROLE : has
    ROLE ||--o{ USER_ROLE : assigned
    ROLE ||--o{ ROLE_PERMISSION : has
    PERMISSION ||--o{ ROLE_PERMISSION : granted
    RESOURCE ||--o{ PERMISSION : controls

    USER {
        int id
        string username
        string email
        string status
    }

    ROLE {
        int id
        string name
        string code
        string description
    }

    PERMISSION {
        int id
        string name
        string code
        string type
    }

    RESOURCE {
        int id
        string name
        string path
        string method
    }
```

## 3. 核心技术栈架构

### 3.1 后端技术栈

#### 3.1.1 基础框架层
```yaml
基础框架:
  语言: Java 17 (LTS版本)
  框架: Spring Boot 3.5.0
  安全: Spring Security 6.x
  核心: Spring Framework 6.x

特性:
  - 原生支持GraalVM
  - 响应式编程支持
  - 云原生特性
  - 性能优化
```

#### 3.1.2 数据访问层
```yaml
数据库:
  主数据库: MySQL 8.x
  文档数据库: MongoDB 5.x
  搜索引擎: Elasticsearch 8.x

连接管理:
  连接池: Druid 1.2.24
  ORM框架: MyBatis-Plus 3.5.5
  数据库驱动: mysql-connector-j 9.1.0

特性:
  - 读写分离
  - 分库分表
  - 数据加密
  - 审计日志
```

#### 3.1.3 缓存与存储层
```yaml
缓存系统:
  分布式缓存: Redis 7.x
  本地缓存: Caffeine 2.9.3
  分布式锁: Redisson 3.40.2

对象存储:
  私有云: MinIO 8.5.10
  公有云: 阿里云OSS 3.16.3

特性:
  - 多级缓存
  - 缓存预热
  - 缓存穿透防护
  - 分布式会话
```

#### 3.1.4 安全认证层
```yaml
认证授权:
  框架: Spring Security 6
  令牌: JWT + Redis
  验证码: Hutool Captcha
  多端登录: Redis会话管理

加密算法:
  密码加密: BCrypt
  数据加密: AES-256
  传输加密: TLS 1.3

特性:
  - 多因子认证
  - 单点登录
  - 会话管理
  - 权限缓存
```

### 3.2 前端技术栈

#### 3.2.1 Web前端架构
```yaml
框架技术:
  核心框架: Vue 3.x / React 18.x
  构建工具: Vite 4.x
  状态管理: Pinia / Redux Toolkit
  路由管理: Vue Router / React Router

UI组件:
  组件库: Element Plus / Ant Design
  图表库: ECharts / Chart.js
  表格组件: AG-Grid
  文件上传: Vue-Upload / React-Dropzone

开发工具:
  语言: TypeScript 5.x
  代码规范: ESLint + Prettier
  测试框架: Vitest / Jest
  包管理: pnpm
```

#### 3.2.2 移动端架构
```yaml
跨平台方案:
  框架: React Native / Flutter
  状态管理: Redux / Provider
  导航: React Navigation / Navigator

原生能力:
  文件选择: react-native-document-picker
  图片处理: react-native-image-picker
  网络请求: Axios
  本地存储: AsyncStorage
```

### 3.3 中间件技术栈

#### 3.3.1 消息队列
```yaml
消息中间件:
  主要选择: RabbitMQ 3.x
  备选方案: Apache Kafka

使用场景:
  - 异步文件处理
  - 批量任务处理
  - 系统解耦
  - 事件驱动

特性:
  - 消息持久化
  - 死信队列
  - 延迟队列
  - 消息确认
```

#### 3.3.2 任务调度
```yaml
调度框架:
  分布式任务: XXL-Job 2.4.2
  本地任务: Spring Task

任务类型:
  - 定时数据清理
  - 批量文件处理
  - 系统监控检查
  - 报表生成

特性:
  - 任务分片
  - 故障转移
  - 执行监控
  - 动态调度
```

## 4. 应用架构设计

### 4.1 分层架构

```mermaid
graph TB
    subgraph "表现层 (Presentation Layer)"
        A1[Controller层]
        A2[Web界面]
        A3[API接口]
        A4[异常处理]
    end

    subgraph "业务层 (Business Layer)"
        B1[Service层]
        B2[业务逻辑]
        B3[事务管理]
        B4[缓存管理]
    end

    subgraph "持久层 (Persistence Layer)"
        C1[Mapper层]
        C2[数据访问]
        C3[SQL映射]
        C4[连接管理]
    end

    subgraph "数据层 (Data Layer)"
        D1[MySQL数据库]
        D2[Redis缓存]
        D3[MongoDB文档]
        D4[文件存储]
    end

    A1 --> B1
    A2 --> A1
    A3 --> A1
    A4 --> A1

    B1 --> C1
    B2 --> B1
    B3 --> B1
    B4 --> B1

    C1 --> D1
    C2 --> C1
    C3 --> C1
    C4 --> C1

    D1 --> D4
    D2 --> D4
    D3 --> D4
```

### 4.2 包结构设计

#### 4.2.1 根包结构
```
com.sinoair.agent
├── SinoairAgentApplication.java        # 应用启动类
├── auth/                               # 认证模块
├── common/                             # 公共模块
├── config/                             # 配置模块
├── core/                               # 核心功能模块
├── module/                             # 业务模块
├── plugin/                             # 插件扩展模块
├── shared/                             # 共享服务模块
└── system/                             # 系统管理模块
```

#### 4.2.2 业务模块结构
```
module/
├── agent/                              # Agent管理模块
│   ├── controller/                     # 控制层
│   ├── service/                        # 业务层
│   ├── mapper/                         # 数据访问层
│   ├── model/                          # 数据模型
│   │   ├── entity/                     # 实体对象
│   │   ├── dto/                        # 数据传输对象
│   │   ├── vo/                         # 视图对象
│   │   └── form/                       # 表单对象
│   └── enums/                          # 枚举定义
├── processing/                         # 文件处理模块
├── fillback/                          # 回填模块
├── website/                           # 网站管理模块
└── llm/                               # LLM集成模块
```

## 5. 核心业务模块架构

### 5.1 Agent管理模块架构

```mermaid
graph TB
    subgraph "Agent管理模块"
        A1[Agent Controller]
        A2[Agent Service]
        A3[Prompt Service]
        A4[Version Service]
        A5[Test Service]
    end

    subgraph "数据层"
        B1[Agent Entity]
        B2[Prompt Entity]
        B3[Version Entity]
        B4[Test Record Entity]
    end

    subgraph "外部服务"
        C1[LLM API]
        C2[文件存储]
        C3[缓存服务]
    end

    A1 --> A2
    A1 --> A3
    A1 --> A4
    A1 --> A5

    A2 --> B1
    A3 --> B2
    A4 --> B3
    A5 --> B4

    A3 --> C1
    A2 --> C2
    A2 --> C3
```

#### Agent数据模型
```java
@Entity
@Table(name = "agents")
public class Agent extends BaseEntity {
    private String name;                    // Agent名称
    private String description;             // 描述
    private String category;                // 分类(AWB/发票/装箱单等)
    private String version;                 // 版本号
    private AgentStatus status;             // 状态
    private Long creatorId;                 // 创建者ID
    private String systemPrompt;            // 系统提示词
    private String userPromptTemplate;      // 用户提示词模板
    private String outputSchema;            // 输出格式定义
    private String validationRules;         // 验证规则
    private AgentConfig config;             // Agent配置
}
```

### 5.2 文件处理模块架构

```mermaid
graph LR
    subgraph "文件处理流程"
        A[文件上传] --> B[格式验证]
        B --> C[预处理]
        C --> D[LLM识别]
        D --> E[结果验证]
        E --> F[格式化输出]
        F --> G[结果存储]
    end

    subgraph "处理服务"
        H1[Upload Service]
        H2[Validation Service]
        H3[Processing Service]
        H4[LLM Service]
        H5[Result Service]
    end

    subgraph "存储层"
        I1[文件存储]
        I2[处理记录]
        I3[结果缓存]
    end

    A --> H1
    B --> H2
    C --> H3
    D --> H4
    E --> H5
    F --> H5
    G --> H5

    H1 --> I1
    H3 --> I2
    H5 --> I3
```

#### 处理记录数据模型
```java
@Entity
@Table(name = "processing_records")
public class ProcessingRecord extends BaseEntity {
    private Long userId;                    // 用户ID
    private Long agentId;                   // Agent ID
    private String fileName;                // 文件名
    private String filePath;                // 文件路径
    private Long fileSize;                  // 文件大小
    private String fileType;                // 文件类型
    private ProcessingStatus status;        // 处理状态
    private String inputData;               // 输入数据
    private String outputData;              // 输出数据
    private String errorMessage;            // 错误信息
    private Integer processingTime;         // 处理时间(毫秒)
    private Date completedAt;               // 完成时间
}
```

### 5.3 回填模块架构

```mermaid
graph TB
    subgraph "回填模块"
        A1[Fillback Controller]
        A2[Fillback Service]
        A3[Website Service]
        A4[Mapping Service]
        A5[Automation Service]
    end

    subgraph "自动化引擎"
        B1[Puppeteer Engine]
        B2[Playwright Engine]
        B3[Selenium Engine]
    end

    subgraph "配置管理"
        C1[Website Config]
        C2[Field Mapping]
        C3[Script Template]
    end

    A1 --> A2
    A2 --> A3
    A2 --> A4
    A2 --> A5

    A5 --> B1
    A5 --> B2
    A5 --> B3

    A3 --> C1
    A4 --> C2
    A5 --> C3
```

#### 网站配置数据模型
```java
@Entity
@Table(name = "websites")
public class Website extends BaseEntity {
    private String name;                    // 网站名称
    private String url;                     // 网站URL
    private String description;             // 描述
    private AuthType authType;              // 认证类型
    private String authConfig;              // 认证配置(JSON)
    private String selectors;               // 页面元素选择器(JSON)
    private WebsiteStatus status;           // 状态
    private String automationScript;        // 自动化脚本
}
```

## 6. 数据架构设计

### 6.1 数据库架构

#### 6.1.1 主数据库设计 (MySQL)
```mermaid
erDiagram
    USERS ||--o{ PROCESSING_RECORDS : creates
    USERS ||--o{ AGENTS : creates
    AGENTS ||--o{ PROCESSING_RECORDS : processes
    AGENTS ||--o{ AGENT_VERSIONS : has
    PROCESSING_RECORDS ||--o{ FILLBACK_RECORDS : generates
    WEBSITES ||--o{ FILLBACK_RECORDS : targets
    AGENTS ||--o{ FIELD_MAPPINGS : maps
    WEBSITES ||--o{ FIELD_MAPPINGS : maps

    USERS {
        bigint id PK
        varchar username UK
        varchar email UK
        varchar password_hash
        varchar role
        varchar department
        varchar status
        datetime created_at
        datetime updated_at
    }

    AGENTS {
        bigint id PK
        varchar name
        text description
        varchar category
        varchar version
        varchar status
        bigint creator_id FK
        text system_prompt
        text user_prompt_template
        json output_schema
        json validation_rules
        datetime created_at
        datetime updated_at
    }

    PROCESSING_RECORDS {
        bigint id PK
        bigint user_id FK
        bigint agent_id FK
        varchar file_name
        varchar file_path
        bigint file_size
        varchar file_type
        varchar status
        json input_data
        json output_data
        text error_message
        int processing_time
        datetime created_at
        datetime completed_at
    }

    WEBSITES {
        bigint id PK
        varchar name
        varchar url
        text description
        varchar auth_type
        json auth_config
        json selectors
        varchar status
        datetime created_at
        datetime updated_at
    }

    FILLBACK_RECORDS {
        bigint id PK
        bigint processing_record_id FK
        bigint website_id FK
        varchar status
        json fillback_data
        text error_message
        int retry_count
        datetime created_at
        datetime completed_at
    }
```

#### 6.1.2 文档数据库设计 (MongoDB)
```javascript
// Agent配置集合
{
  "_id": ObjectId,
  "agentId": NumberLong,
  "promptHistory": [
    {
      "version": "1.0.0",
      "systemPrompt": "...",
      "userPromptTemplate": "...",
      "outputSchema": {...},
      "createdAt": ISODate,
      "performance": {
        "accuracy": 0.95,
        "avgProcessingTime": 15000
      }
    }
  ],
  "testCases": [
    {
      "fileName": "test-awb.pdf",
      "expectedOutput": {...},
      "actualOutput": {...},
      "passed": true,
      "createdAt": ISODate
    }
  ]
}

// 处理日志集合
{
  "_id": ObjectId,
  "processingId": NumberLong,
  "steps": [
    {
      "step": "file_upload",
      "timestamp": ISODate,
      "duration": 1000,
      "status": "success",
      "details": {...}
    },
    {
      "step": "llm_processing",
      "timestamp": ISODate,
      "duration": 15000,
      "status": "success",
      "llmRequest": {...},
      "llmResponse": {...}
    }
  ]
}
```

### 6.2 缓存架构设计

#### 6.2.1 Redis缓存策略
```yaml
缓存分层:
  L1缓存: Caffeine本地缓存
    - 用户会话信息
    - 系统配置信息
    - 热点数据

  L2缓存: Redis分布式缓存
    - 用户认证Token
    - Agent配置信息
    - 处理结果缓存
    - 网站配置缓存

缓存Key设计:
  用户相关: "user:{userId}:*"
  Agent相关: "agent:{agentId}:*"
  处理相关: "processing:{recordId}:*"
  网站相关: "website:{websiteId}:*"

过期策略:
  用户Token: 24小时
  Agent配置: 1小时
  处理结果: 7天
  网站配置: 30分钟
```

#### 6.2.2 缓存更新策略
```mermaid
graph LR
    A[数据更新] --> B{缓存策略}
    B -->|写入| C[Cache Aside]
    B -->|更新| D[Write Through]
    B -->|删除| E[Cache Invalidation]

    C --> F[先更新DB]
    F --> G[删除缓存]

    D --> H[同时更新DB和缓存]

    E --> I[删除相关缓存]
    I --> J[下次访问重建]
```

## 7. 安全架构设计

### 7.1 认证架构

#### 7.1.1 多重认证机制
```mermaid
graph TB
    subgraph "认证方式"
        A1[用户名密码]
        A2[短信验证码]
        A3[邮箱验证]
        A4[微信登录]
        A5[LDAP集成]
    end

    subgraph "认证流程"
        B1[身份验证]
        B2[Token生成]
        B3[权限加载]
        B4[会话管理]
    end

    subgraph "安全控制"
        C1[登录限制]
        C2[设备绑定]
        C3[异地登录检测]
        C4[会话超时]
    end

    A1 --> B1
    A2 --> B1
    A3 --> B1
    A4 --> B1
    A5 --> B1

    B1 --> B2
    B2 --> B3
    B3 --> B4

    B4 --> C1
    B4 --> C2
    B4 --> C3
    B4 --> C4
```

#### 7.1.2 JWT Token设计
```java
public class JwtTokenProvider {

    // Token结构
    public class TokenClaims {
        private Long userId;                // 用户ID
        private String username;            // 用户名
        private String role;                // 角色
        private List<String> permissions;   // 权限列表
        private String deviceId;            // 设备ID
        private Long issuedAt;              // 签发时间
        private Long expiresAt;             // 过期时间
    }

    // Token生成
    public String generateToken(User user, String deviceId) {
        return Jwts.builder()
            .setSubject(user.getUsername())
            .claim("userId", user.getId())
            .claim("role", user.getRole())
            .claim("permissions", user.getPermissions())
            .claim("deviceId", deviceId)
            .setIssuedAt(new Date())
            .setExpiration(new Date(System.currentTimeMillis() + expiration))
            .signWith(SignatureAlgorithm.HS512, secret)
            .compact();
    }
}
```

### 7.2 授权架构

#### 7.2.1 权限控制模型
```mermaid
graph TB
    subgraph "权限层级"
        A1[系统权限]
        A2[模块权限]
        A3[功能权限]
        A4[数据权限]
    end

    subgraph "权限类型"
        B1[菜单权限]
        B2[按钮权限]
        B3[API权限]
        B4[字段权限]
    end

    subgraph "权限控制"
        C1[角色权限]
        C2[用户权限]
        C3[部门权限]
        C4[数据范围]
    end

    A1 --> A2
    A2 --> A3
    A3 --> A4

    B1 --> C1
    B2 --> C1
    B3 --> C2
    B4 --> C3

    C1 --> C4
    C2 --> C4
    C3 --> C4
```

#### 7.2.2 权限注解设计
```java
// 方法级权限控制
@PreAuthorize("hasRole('ADMIN') or hasPermission(#agentId, 'AGENT', 'READ')")
public Agent getAgent(@PathVariable Long agentId) {
    return agentService.getById(agentId);
}

// 数据权限控制
@DataScope(deptAlias = "d", userAlias = "u")
public List<ProcessingRecord> getProcessingRecords(ProcessingQuery query) {
    return processingService.list(query);
}

// API访问控制
@RateLimiter(key = "processing", rate = 10, interval = 60)
@RequiresPermissions("processing:upload")
public Result<String> uploadFile(@RequestParam MultipartFile file) {
    return processingService.uploadFile(file);
}
```

### 7.3 数据安全架构

#### 7.3.1 数据加密策略
```yaml
传输加密:
  协议: TLS 1.3
  证书: SSL证书
  算法: ECDHE-RSA-AES256-GCM-SHA384

存储加密:
  数据库: AES-256-CBC
  文件: AES-256-GCM
  配置: Jasypt加密

字段加密:
  敏感字段: @Encrypted注解
  加密算法: AES-256
  密钥管理: KMS密钥管理服务
```

#### 7.3.2 数据脱敏策略
```java
@Component
public class DataMaskingService {

    // 手机号脱敏
    @DataMasking(type = MaskingType.MOBILE)
    private String mobile;

    // 邮箱脱敏
    @DataMasking(type = MaskingType.EMAIL)
    private String email;

    // 身份证脱敏
    @DataMasking(type = MaskingType.ID_CARD)
    private String idCard;

    // 自定义脱敏规则
    @DataMasking(pattern = "(?<=.{2}).*(?=.{2})")
    private String customField;
}
```

## 8. 集成架构设计

### 8.1 LLM访问设计架构

#### 8.1.1 LLM统一访问架构
```mermaid
graph TB
    subgraph "业务层"
        A1[Agent Service]
        A2[Processing Service]
        A3[Batch Service]
    end

    subgraph "LLM统一访问层"
        B1[LLM Gateway]
        B2[Request Validator]
        B3[Response Formatter]
        B4[Rate Limiter]
    end

    subgraph "LLM适配层"
        C1[LLM Service Interface]
        C2[OpenAI Adapter]
        C3[Claude Adapter]
        C4[文心一言 Adapter]
        C5[通义千问 Adapter]
        C6[智谱AI Adapter]
    end

    subgraph "模型管理层"
        D1[Model Registry]
        D2[Model Router]
        D3[Load Balancer]
        D4[Fallback Handler]
        D5[Health Monitor]
    end

    subgraph "请求处理层"
        E1[Request Builder]
        E2[Response Parser]
        E3[Error Handler]
        E4[Retry Manager]
        E5[Cache Manager]
    end

    subgraph "配置管理层"
        F1[Model Config]
        F2[API Key Manager]
        F3[Quota Manager]
        F4[Performance Monitor]
    end

    A1 --> B1
    A2 --> B1
    A3 --> B1

    B1 --> B2
    B2 --> B3
    B3 --> B4
    B4 --> C1

    C1 --> C2
    C1 --> C3
    C1 --> C4
    C1 --> C5
    C1 --> C6

    C2 --> D1
    C3 --> D1
    C4 --> D1
    C5 --> D1
    C6 --> D1

    D1 --> D2
    D2 --> D3
    D3 --> D4
    D4 --> D5

    D2 --> E1
    E1 --> E2
    E2 --> E3
    E3 --> E4
    E4 --> E5

    D1 --> F1
    F1 --> F2
    F2 --> F3
    F3 --> F4
```

#### 8.1.2 统一请求响应模型设计

##### 统一请求模型
```java
/**
 * LLM统一请求模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LLMRequest {

    /**
     * 请求ID，用于追踪
     */
    private String requestId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * Agent ID
     */
    private Long agentId;

    /**
     * 模型类型（可选，用于指定特定模型）
     */
    private ModelType modelType;

    /**
     * 系统提示词
     */
    private String systemPrompt;

    /**
     * 用户提示词
     */
    private String userPrompt;

    /**
     * 输入文件列表
     */
    private List<InputFile> files;

    /**
     * 输出格式定义
     */
    private OutputSchema outputSchema;

    /**
     * 模型参数
     */
    private ModelParameters parameters;

    /**
     * 请求配置
     */
    private RequestConfig config;

    /**
     * 扩展参数
     */
    private Map<String, Object> extensions;
}

/**
 * 输入文件模型
 */
@Data
public class InputFile {
    private String fileName;
    private String fileType;
    private String fileUrl;
    private String base64Content;
    private Long fileSize;
    private String mimeType;
}

/**
 * 输出格式定义
 */
@Data
public class OutputSchema {
    private String format; // JSON, XML, TEXT
    private String schema; // JSON Schema定义
    private Map<String, Object> properties;
    private List<String> required;
}

/**
 * 模型参数
 */
@Data
@Builder
public class ModelParameters {
    private Double temperature = 0.1;
    private Integer maxTokens = 4000;
    private Double topP = 0.9;
    private Integer topK = 40;
    private Double frequencyPenalty = 0.0;
    private Double presencePenalty = 0.0;
    private List<String> stopSequences;
}

/**
 * 请求配置
 */
@Data
@Builder
public class RequestConfig {
    private Integer timeout = 60; // 超时时间（秒）
    private Integer retryCount = 3; // 重试次数
    private Boolean enableCache = true; // 是否启用缓存
    private Integer priority = 5; // 优先级 1-10
    private Boolean async = false; // 是否异步处理
}
```

##### 统一响应模型
```java
/**
 * LLM统一响应模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LLMResponse {

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 响应状态
     */
    private ResponseStatus status;

    /**
     * 响应内容
     */
    private String content;

    /**
     * 结构化数据（如果输出格式为JSON）
     */
    private Map<String, Object> structuredData;

    /**
     * 使用的模型信息
     */
    private ModelInfo modelInfo;

    /**
     * 使用统计
     */
    private UsageStats usage;

    /**
     * 处理时间信息
     */
    private TimingInfo timing;

    /**
     * 错误信息（如果有）
     */
    private ErrorInfo error;

    /**
     * 扩展信息
     */
    private Map<String, Object> metadata;
}

/**
 * 响应状态枚举
 */
public enum ResponseStatus {
    SUCCESS,        // 成功
    PARTIAL,        // 部分成功
    FAILED,         // 失败
    TIMEOUT,        // 超时
    RATE_LIMITED,   // 限流
    QUOTA_EXCEEDED, // 配额超限
    MODEL_ERROR     // 模型错误
}

/**
 * 模型信息
 */
@Data
public class ModelInfo {
    private String provider;     // 提供商：openai, anthropic, baidu, alibaba
    private String modelName;    // 模型名称
    private String version;      // 模型版本
    private String endpoint;     // 调用端点
}

/**
 * 使用统计
 */
@Data
public class UsageStats {
    private Integer promptTokens;      // 输入Token数
    private Integer completionTokens;  // 输出Token数
    private Integer totalTokens;       // 总Token数
    private Double cost;               // 费用（如果有）
}

/**
 * 时间信息
 */
@Data
public class TimingInfo {
    private Long requestTime;    // 请求时间戳
    private Long responseTime;   // 响应时间戳
    private Long duration;       // 处理时长（毫秒）
    private Long queueTime;      // 队列等待时间
    private Long processTime;    // 实际处理时间
}

/**
 * 错误信息
 */
@Data
public class ErrorInfo {
    private String code;         // 错误码
    private String message;      // 错误消息
    private String detail;       // 详细信息
    private String suggestion;   // 建议解决方案
}
```

#### 8.1.3 LLM服务接口设计

##### 核心服务接口
```java
/**
 * LLM统一服务接口
 */
public interface LLMService {

    /**
     * 同步处理文档识别请求
     * @param request LLM请求
     * @return LLM响应
     */
    LLMResponse processDocument(LLMRequest request);

    /**
     * 异步处理文档识别请求
     * @param request LLM请求
     * @return 异步响应Future
     */
    CompletableFuture<LLMResponse> processDocumentAsync(LLMRequest request);

    /**
     * 批量处理文档
     * @param requests 批量请求列表
     * @return 批量响应列表
     */
    List<LLMResponse> batchProcessDocuments(List<LLMRequest> requests);

    /**
     * 流式处理（支持大文件）
     * @param request LLM请求
     * @return 流式响应
     */
    Flux<LLMResponse> streamProcess(LLMRequest request);

    /**
     * 获取模型健康状态
     * @param modelType 模型类型
     * @return 健康状态
     */
    HealthStatus getModelHealth(ModelType modelType);

    /**
     * 获取模型使用统计
     * @param modelType 模型类型
     * @param timeRange 时间范围
     * @return 使用统计
     */
    ModelUsageStats getUsageStats(ModelType modelType, TimeRange timeRange);
}

/**
 * LLM服务实现类
 */
@Service
@Slf4j
public class LLMServiceImpl implements LLMService {

    @Autowired
    private LLMGateway llmGateway;

    @Autowired
    private ModelRouter modelRouter;

    @Autowired
    private RetryTemplate retryTemplate;

    @Autowired
    private CacheManager cacheManager;

    @Override
    public LLMResponse processDocument(LLMRequest request) {
        // 1. 请求验证
        validateRequest(request);

        // 2. 检查缓存
        String cacheKey = generateCacheKey(request);
        LLMResponse cachedResponse = getCachedResponse(cacheKey);
        if (cachedResponse != null && request.getConfig().getEnableCache()) {
            log.info("返回缓存结果，requestId: {}", request.getRequestId());
            return cachedResponse;
        }

        // 3. 路由到具体模型
        return retryTemplate.execute(context -> {
            LLMAdapter adapter = modelRouter.route(request);
            LLMResponse response = adapter.process(request);

            // 4. 缓存结果
            if (response.getStatus() == ResponseStatus.SUCCESS && request.getConfig().getEnableCache()) {
                cacheResponse(cacheKey, response);
            }

            return response;
        });
    }

    @Override
    @Async("llmTaskExecutor")
    public CompletableFuture<LLMResponse> processDocumentAsync(LLMRequest request) {
        try {
            LLMResponse response = processDocument(request);
            return CompletableFuture.completedFuture(response);
        } catch (Exception e) {
            log.error("异步处理失败，requestId: {}", request.getRequestId(), e);
            CompletableFuture<LLMResponse> future = new CompletableFuture<>();
            future.completeExceptionally(e);
            return future;
        }
    }

    @Override
    public List<LLMResponse> batchProcessDocuments(List<LLMRequest> requests) {
        return requests.parallelStream()
                .map(this::processDocument)
                .collect(Collectors.toList());
    }

    private void validateRequest(LLMRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("请求不能为空");
        }
        if (StringUtils.isBlank(request.getUserPrompt())) {
            throw new IllegalArgumentException("用户提示词不能为空");
        }
        if (CollectionUtils.isEmpty(request.getFiles()) && StringUtils.isBlank(request.getUserPrompt())) {
            throw new IllegalArgumentException("文件和提示词不能同时为空");
        }
    }

    private String generateCacheKey(LLMRequest request) {
        return DigestUtils.md5Hex(
            request.getSystemPrompt() +
            request.getUserPrompt() +
            request.getFiles().toString()
        );
    }
}
```

##### LLM适配器接口
```java
/**
 * LLM适配器接口
 */
public interface LLMAdapter {

    /**
     * 处理请求
     */
    LLMResponse process(LLMRequest request);

    /**
     * 获取支持的模型类型
     */
    ModelType getSupportedModelType();

    /**
     * 检查健康状态
     */
    HealthStatus checkHealth();

    /**
     * 获取模型信息
     */
    ModelInfo getModelInfo();
}

/**
 * OpenAI适配器实现
 */
@Component
@ConditionalOnProperty(name = "llm.openai.enabled", havingValue = "true")
public class OpenAIAdapter implements LLMAdapter {

    @Autowired
    private OpenAIClient openAIClient;

    @Override
    public LLMResponse process(LLMRequest request) {
        try {
            // 构建OpenAI请求
            ChatCompletionRequest openAIRequest = buildOpenAIRequest(request);

            // 调用OpenAI API
            ChatCompletionResponse openAIResponse = openAIClient.chatCompletion(openAIRequest);

            // 转换为统一响应格式
            return convertToLLMResponse(openAIResponse, request);

        } catch (Exception e) {
            log.error("OpenAI调用失败", e);
            return buildErrorResponse(request, e);
        }
    }

    @Override
    public ModelType getSupportedModelType() {
        return ModelType.OPENAI;
    }

    private ChatCompletionRequest buildOpenAIRequest(LLMRequest request) {
        return ChatCompletionRequest.builder()
                .model("gpt-4-vision-preview")
                .messages(buildMessages(request))
                .temperature(request.getParameters().getTemperature())
                .maxTokens(request.getParameters().getMaxTokens())
                .build();
    }

    private LLMResponse convertToLLMResponse(ChatCompletionResponse openAIResponse, LLMRequest request) {
        return LLMResponse.builder()
                .requestId(request.getRequestId())
                .status(ResponseStatus.SUCCESS)
                .content(openAIResponse.getChoices().get(0).getMessage().getContent())
                .modelInfo(ModelInfo.builder()
                        .provider("openai")
                        .modelName(openAIResponse.getModel())
                        .build())
                .usage(UsageStats.builder()
                        .promptTokens(openAIResponse.getUsage().getPromptTokens())
                        .completionTokens(openAIResponse.getUsage().getCompletionTokens())
                        .totalTokens(openAIResponse.getUsage().getTotalTokens())
                        .build())
                .timing(TimingInfo.builder()
                        .responseTime(System.currentTimeMillis())
                        .build())
                .build();
    }
}

/**
 * Claude适配器实现
 */
@Component
@ConditionalOnProperty(name = "llm.claude.enabled", havingValue = "true")
public class ClaudeAdapter implements LLMAdapter {

    @Autowired
    private ClaudeClient claudeClient;

    @Override
    public LLMResponse process(LLMRequest request) {
        try {
            // 构建Claude请求
            ClaudeRequest claudeRequest = buildClaudeRequest(request);

            // 调用Claude API
            ClaudeResponse claudeResponse = claudeClient.complete(claudeRequest);

            // 转换为统一响应格式
            return convertToLLMResponse(claudeResponse, request);

        } catch (Exception e) {
            log.error("Claude调用失败", e);
            return buildErrorResponse(request, e);
        }
    }

    @Override
    public ModelType getSupportedModelType() {
        return ModelType.CLAUDE;
    }
}

/**
 * 文心一言适配器实现
 */
@Component
@ConditionalOnProperty(name = "llm.wenxin.enabled", havingValue = "true")
public class WenxinAdapter implements LLMAdapter {

    @Autowired
    private WenxinClient wenxinClient;

    @Override
    public LLMResponse process(LLMRequest request) {
        try {
            // 构建文心一言请求
            WenxinRequest wenxinRequest = buildWenxinRequest(request);

            // 调用文心一言API
            WenxinResponse wenxinResponse = wenxinClient.chat(wenxinRequest);

            // 转换为统一响应格式
            return convertToLLMResponse(wenxinResponse, request);

        } catch (Exception e) {
            log.error("文心一言调用失败", e);
            return buildErrorResponse(request, e);
        }
    }

    @Override
    public ModelType getSupportedModelType() {
        return ModelType.WENXIN;
    }
}
```

#### 8.1.4 模型路由与负载均衡

##### 模型路由器
```java
/**
 * 模型路由器
 */
@Component
public class ModelRouter {

    @Autowired
    private List<LLMAdapter> adapters;

    @Autowired
    private ModelRegistry modelRegistry;

    @Autowired
    private LoadBalancer loadBalancer;

    /**
     * 路由请求到合适的模型
     */
    public LLMAdapter route(LLMRequest request) {
        // 1. 如果指定了模型类型，直接路由
        if (request.getModelType() != null) {
            return getAdapterByType(request.getModelType());
        }

        // 2. 根据Agent配置选择模型
        if (request.getAgentId() != null) {
            ModelType preferredModel = getAgentPreferredModel(request.getAgentId());
            if (preferredModel != null) {
                LLMAdapter adapter = getAdapterByType(preferredModel);
                if (isModelHealthy(preferredModel)) {
                    return adapter;
                }
            }
        }

        // 3. 负载均衡选择可用模型
        return loadBalancer.selectAdapter(getHealthyAdapters());
    }

    private LLMAdapter getAdapterByType(ModelType modelType) {
        return adapters.stream()
                .filter(adapter -> adapter.getSupportedModelType() == modelType)
                .findFirst()
                .orElseThrow(() -> new UnsupportedModelException("不支持的模型类型: " + modelType));
    }

    private List<LLMAdapter> getHealthyAdapters() {
        return adapters.stream()
                .filter(adapter -> adapter.checkHealth().isHealthy())
                .collect(Collectors.toList());
    }

    private boolean isModelHealthy(ModelType modelType) {
        return getAdapterByType(modelType).checkHealth().isHealthy();
    }

    private ModelType getAgentPreferredModel(Long agentId) {
        // 从Agent配置中获取首选模型
        return modelRegistry.getAgentPreferredModel(agentId);
    }
}

/**
 * 负载均衡器
 */
@Component
public class LoadBalancer {

    private final AtomicInteger counter = new AtomicInteger(0);

    /**
     * 轮询选择适配器
     */
    public LLMAdapter selectAdapter(List<LLMAdapter> adapters) {
        if (adapters.isEmpty()) {
            throw new NoAvailableModelException("没有可用的模型");
        }

        int index = counter.getAndIncrement() % adapters.size();
        return adapters.get(index);
    }

    /**
     * 加权轮询选择（根据模型性能权重）
     */
    public LLMAdapter selectAdapterByWeight(List<LLMAdapter> adapters, Map<ModelType, Integer> weights) {
        // 实现加权轮询逻辑
        List<LLMAdapter> weightedAdapters = new ArrayList<>();
        for (LLMAdapter adapter : adapters) {
            int weight = weights.getOrDefault(adapter.getSupportedModelType(), 1);
            for (int i = 0; i < weight; i++) {
                weightedAdapters.add(adapter);
            }
        }

        int index = counter.getAndIncrement() % weightedAdapters.size();
        return weightedAdapters.get(index);
    }
}
```

#### 8.1.5 配置管理与监控

##### 模型配置管理
```java
/**
 * 模型配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "llm")
public class LLMConfig {

    /**
     * OpenAI配置
     */
    private OpenAIConfig openai = new OpenAIConfig();

    /**
     * Claude配置
     */
    private ClaudeConfig claude = new ClaudeConfig();

    /**
     * 文心一言配置
     */
    private WenxinConfig wenxin = new WenxinConfig();

    /**
     * 通义千问配置
     */
    private QwenConfig qwen = new QwenConfig();

    /**
     * 全局配置
     */
    private GlobalConfig global = new GlobalConfig();

    @Data
    public static class OpenAIConfig {
        private boolean enabled = false;
        private String apiKey;
        private String baseUrl = "https://api.openai.com/v1";
        private String model = "gpt-4-vision-preview";
        private int timeout = 60;
        private int maxRetries = 3;
        private double temperature = 0.1;
        private int maxTokens = 4000;
        private int rateLimit = 100; // 每分钟请求数
    }

    @Data
    public static class ClaudeConfig {
        private boolean enabled = false;
        private String apiKey;
        private String baseUrl = "https://api.anthropic.com";
        private String model = "claude-3-opus-20240229";
        private int timeout = 60;
        private int maxRetries = 3;
        private double temperature = 0.1;
        private int maxTokens = 4000;
        private int rateLimit = 50;
    }

    @Data
    public static class WenxinConfig {
        private boolean enabled = false;
        private String apiKey;
        private String secretKey;
        private String baseUrl = "https://aip.baidubce.com";
        private String model = "ERNIE-Bot-4";
        private int timeout = 60;
        private int maxRetries = 3;
        private double temperature = 0.1;
        private int maxTokens = 4000;
        private int rateLimit = 200;
    }

    @Data
    public static class QwenConfig {
        private boolean enabled = false;
        private String apiKey;
        private String baseUrl = "https://dashscope.aliyuncs.com/api/v1";
        private String model = "qwen-vl-plus";
        private int timeout = 60;
        private int maxRetries = 3;
        private double temperature = 0.1;
        private int maxTokens = 4000;
        private int rateLimit = 100;
    }

    @Data
    public static class GlobalConfig {
        private boolean enableCache = true;
        private int cacheExpireMinutes = 60;
        private boolean enableMetrics = true;
        private boolean enableHealthCheck = true;
        private int healthCheckIntervalSeconds = 30;
        private String fallbackStrategy = "ROUND_ROBIN"; // ROUND_ROBIN, WEIGHTED, FASTEST
    }
}
```

##### 模型注册表与监控
```java
/**
 * 模型注册表
 */
@Component
public class ModelRegistry {

    @Autowired
    private LLMConfig llmConfig;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private final Map<ModelType, ModelMetadata> modelMetadataMap = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        registerModels();
        startHealthCheck();
    }

    /**
     * 注册所有可用模型
     */
    private void registerModels() {
        if (llmConfig.getOpenai().isEnabled()) {
            registerModel(ModelType.OPENAI, createOpenAIMetadata());
        }
        if (llmConfig.getClaude().isEnabled()) {
            registerModel(ModelType.CLAUDE, createClaudeMetadata());
        }
        if (llmConfig.getWenxin().isEnabled()) {
            registerModel(ModelType.WENXIN, createWenxinMetadata());
        }
        if (llmConfig.getQwen().isEnabled()) {
            registerModel(ModelType.QWEN, createQwenMetadata());
        }
    }

    /**
     * 获取Agent首选模型
     */
    public ModelType getAgentPreferredModel(Long agentId) {
        String key = "agent:model:" + agentId;
        String modelType = (String) redisTemplate.opsForValue().get(key);
        return modelType != null ? ModelType.valueOf(modelType) : null;
    }

    /**
     * 设置Agent首选模型
     */
    public void setAgentPreferredModel(Long agentId, ModelType modelType) {
        String key = "agent:model:" + agentId;
        redisTemplate.opsForValue().set(key, modelType.name(), Duration.ofDays(30));
    }

    /**
     * 获取模型元数据
     */
    public ModelMetadata getModelMetadata(ModelType modelType) {
        return modelMetadataMap.get(modelType);
    }

    private void registerModel(ModelType modelType, ModelMetadata metadata) {
        modelMetadataMap.put(modelType, metadata);
        log.info("注册模型: {} - {}", modelType, metadata.getDisplayName());
    }

    private void startHealthCheck() {
        if (llmConfig.getGlobal().isEnableHealthCheck()) {
            ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
            scheduler.scheduleAtFixedRate(
                this::performHealthCheck,
                0,
                llmConfig.getGlobal().getHealthCheckIntervalSeconds(),
                TimeUnit.SECONDS
            );
        }
    }

    private void performHealthCheck() {
        modelMetadataMap.forEach((modelType, metadata) -> {
            try {
                // 执行健康检查逻辑
                boolean isHealthy = checkModelHealth(modelType);
                metadata.setHealthy(isHealthy);
                metadata.setLastHealthCheck(System.currentTimeMillis());
            } catch (Exception e) {
                log.error("模型健康检查失败: {}", modelType, e);
                metadata.setHealthy(false);
            }
        });
    }
}

/**
 * 模型元数据
 */
@Data
@Builder
public class ModelMetadata {
    private ModelType modelType;
    private String displayName;
    private String provider;
    private String version;
    private boolean enabled;
    private boolean healthy;
    private long lastHealthCheck;
    private int rateLimit;
    private double costPerToken;
    private int maxTokens;
    private List<String> supportedFormats;
    private Map<String, Object> capabilities;
}

/**
 * 模型类型枚举
 */
public enum ModelType {
    OPENAI("OpenAI", "gpt-4-vision-preview"),
    CLAUDE("Claude", "claude-3-opus-20240229"),
    WENXIN("文心一言", "ERNIE-Bot-4"),
    QWEN("通义千问", "qwen-vl-plus"),
    ZHIPU("智谱AI", "glm-4v");

    private final String displayName;
    private final String defaultModel;

    ModelType(String displayName, String defaultModel) {
        this.displayName = displayName;
        this.defaultModel = defaultModel;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDefaultModel() {
        return defaultModel;
    }
}
```

#### 8.1.6 使用示例

##### 业务层调用示例
```java
/**
 * Agent处理服务
 */
@Service
public class AgentProcessingService {

    @Autowired
    private LLMService llmService;

    /**
     * 处理AWB文档
     */
    public ProcessingResult processAWBDocument(Long agentId, MultipartFile file) {
        try {
            // 1. 构建LLM请求
            LLMRequest request = LLMRequest.builder()
                    .requestId(UUID.randomUUID().toString())
                    .agentId(agentId)
                    .systemPrompt(getAgentSystemPrompt(agentId))
                    .userPrompt("请识别这个AWB文档中的所有字段信息")
                    .files(Arrays.asList(convertToInputFile(file)))
                    .outputSchema(getAWBOutputSchema())
                    .parameters(ModelParameters.builder()
                            .temperature(0.1)
                            .maxTokens(4000)
                            .build())
                    .config(RequestConfig.builder()
                            .timeout(60)
                            .retryCount(3)
                            .enableCache(true)
                            .build())
                    .build();

            // 2. 调用LLM服务
            LLMResponse response = llmService.processDocument(request);

            // 3. 处理响应
            if (response.getStatus() == ResponseStatus.SUCCESS) {
                return ProcessingResult.success(response.getStructuredData());
            } else {
                return ProcessingResult.failed(response.getError().getMessage());
            }

        } catch (Exception e) {
            log.error("AWB文档处理失败", e);
            return ProcessingResult.failed("文档处理失败: " + e.getMessage());
        }
    }

    /**
     * 批量处理文档
     */
    public List<ProcessingResult> batchProcessDocuments(Long agentId, List<MultipartFile> files) {
        List<LLMRequest> requests = files.stream()
                .map(file -> buildLLMRequest(agentId, file))
                .collect(Collectors.toList());

        List<LLMResponse> responses = llmService.batchProcessDocuments(requests);

        return responses.stream()
                .map(this::convertToProcessingResult)
                .collect(Collectors.toList());
    }
}
```

#### 8.1.7 配置文件示例

##### application.yml配置示例
```yaml
# LLM配置
llm:
  # 全局配置
  global:
    enable-cache: true
    cache-expire-minutes: 60
    enable-metrics: true
    enable-health-check: true
    health-check-interval-seconds: 30
    fallback-strategy: ROUND_ROBIN # ROUND_ROBIN, WEIGHTED, FASTEST

  # OpenAI配置
  openai:
    enabled: true
    api-key: ${OPENAI_API_KEY:sk-xxx}
    base-url: https://api.openai.com/v1
    model: gpt-4-vision-preview
    timeout: 60
    max-retries: 3
    temperature: 0.1
    max-tokens: 4000
    rate-limit: 100 # 每分钟请求数

  # Claude配置
  claude:
    enabled: true
    api-key: ${CLAUDE_API_KEY:sk-ant-xxx}
    base-url: https://api.anthropic.com
    model: claude-3-opus-20240229
    timeout: 60
    max-retries: 3
    temperature: 0.1
    max-tokens: 4000
    rate-limit: 50

  # 文心一言配置
  wenxin:
    enabled: true
    api-key: ${WENXIN_API_KEY:xxx}
    secret-key: ${WENXIN_SECRET_KEY:xxx}
    base-url: https://aip.baidubce.com
    model: ERNIE-Bot-4
    timeout: 60
    max-retries: 3
    temperature: 0.1
    max-tokens: 4000
    rate-limit: 200

  # 通义千问配置
  qwen:
    enabled: true
    api-key: ${QWEN_API_KEY:sk-xxx}
    base-url: https://dashscope.aliyuncs.com/api/v1
    model: qwen-vl-plus
    timeout: 60
    max-retries: 3
    temperature: 0.1
    max-tokens: 4000
    rate-limit: 100

# 线程池配置
spring:
  task:
    execution:
      pool:
        core-size: 10
        max-size: 50
        queue-capacity: 200
        thread-name-prefix: llm-task-
        keep-alive: 60s

# 缓存配置
spring:
  cache:
    type: redis
    redis:
      time-to-live: 3600000 # 1小时
      cache-null-values: false

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,metrics,prometheus
  metrics:
    export:
      prometheus:
        enabled: true
    tags:
      application: sinoair-agent
```

### 8.2 自动化集成架构

#### 8.2.1 浏览器自动化架构
```mermaid
graph LR
    subgraph "自动化引擎"
        A1[Automation Manager]
        A2[Puppeteer Engine]
        A3[Playwright Engine]
        A4[Selenium Engine]
    end

    subgraph "脚本管理"
        B1[Script Repository]
        B2[Script Executor]
        B3[Script Validator]
        B4[Script Generator]
    end

    subgraph "浏览器管理"
        C1[Browser Pool]
        C2[Session Manager]
        C3[Resource Monitor]
        C4[Cleanup Service]
    end

    A1 --> A2
    A1 --> A3
    A1 --> A4

    A2 --> B1
    A3 --> B1
    A4 --> B1

    B1 --> B2
    B2 --> B3
    B3 --> B4

    B2 --> C1
    C1 --> C2
    C2 --> C3
    C3 --> C4
```

#### 8.2.2 自动化脚本模板
```javascript
// Puppeteer脚本模板
class FillbackScript {

    async execute(page, data, config) {
        try {
            // 1. 导航到目标页面
            await page.goto(config.url, { waitUntil: 'networkidle2' });

            // 2. 执行登录
            if (config.authRequired) {
                await this.login(page, config.auth);
            }

            // 3. 填写表单数据
            await this.fillForm(page, data, config.selectors);

            // 4. 提交表单
            await this.submitForm(page, config.submitSelector);

            // 5. 验证结果
            return await this.validateResult(page, config.successSelector);

        } catch (error) {
            throw new FillbackException(`脚本执行失败: ${error.message}`);
        }
    }

    async fillForm(page, data, selectors) {
        for (const [field, selector] of Object.entries(selectors)) {
            if (data[field]) {
                await page.waitForSelector(selector);
                await page.type(selector, data[field]);
            }
        }
    }
}
```

### 8.3 第三方服务集成

#### 8.3.1 对象存储集成
```java
@Component
public class FileStorageService {

    @Autowired
    private MinIOClient minioClient;

    @Autowired
    private OSSClient ossClient;

    /**
     * 上传文件
     */
    public String uploadFile(MultipartFile file, String bucket) {
        try {
            String fileName = generateFileName(file.getOriginalFilename());

            // 根据配置选择存储方式
            if (storageConfig.getType().equals("minio")) {
                return uploadToMinio(file, bucket, fileName);
            } else {
                return uploadToOSS(file, bucket, fileName);
            }
        } catch (Exception e) {
            throw new FileUploadException("文件上传失败", e);
        }
    }

    /**
     * 下载文件
     */
    public InputStream downloadFile(String bucket, String fileName) {
        try {
            if (storageConfig.getType().equals("minio")) {
                return minioClient.getObject(
                    GetObjectArgs.builder()
                        .bucket(bucket)
                        .object(fileName)
                        .build()
                );
            } else {
                return ossClient.getObject(bucket, fileName).getObjectContent();
            }
        } catch (Exception e) {
            throw new FileDownloadException("文件下载失败", e);
        }
    }
}
```

## 9. 部署架构设计

### 9.1 容器化架构

#### 9.1.1 Docker容器架构
```mermaid
graph TB
    subgraph "容器编排"
        A1[Docker Compose]
        A2[Kubernetes]
        A3[Docker Swarm]
    end

    subgraph "应用容器"
        B1[Frontend Container]
        B2[Backend Container]
        B3[Gateway Container]
        B4[Worker Container]
    end

    subgraph "中间件容器"
        C1[MySQL Container]
        C2[Redis Container]
        C3[MongoDB Container]
        C4[RabbitMQ Container]
    end

    subgraph "监控容器"
        D1[Prometheus Container]
        D2[Grafana Container]
        D3[ELK Container]
    end

    A1 --> B1
    A1 --> B2
    A1 --> B3
    A1 --> B4

    A1 --> C1
    A1 --> C2
    A1 --> C3
    A1 --> C4

    A1 --> D1
    A1 --> D2
    A1 --> D3
```

#### 9.1.2 Docker Compose配置
```yaml
version: '3.8'

services:
  # 前端服务
  frontend:
    build: ./frontend
    ports:
      - "80:80"
    depends_on:
      - gateway
    networks:
      - sinoair-network

  # API网关
  gateway:
    build: ./gateway
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
    depends_on:
      - backend
      - redis
    networks:
      - sinoair-network

  # 后端服务
  backend:
    build: ./backend
    ports:
      - "8081:8081"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - MYSQL_HOST=mysql
      - REDIS_HOST=redis
      - MONGODB_HOST=mongodb
    depends_on:
      - mysql
      - redis
      - mongodb
    networks:
      - sinoair-network

  # 文件处理服务
  processor:
    build: ./processor
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - RABBITMQ_HOST=rabbitmq
    depends_on:
      - rabbitmq
      - mongodb
    networks:
      - sinoair-network

  # MySQL数据库
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: sinoair_agent
      MYSQL_USER: sinoair
      MYSQL_PASSWORD: sinoair123
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql:/docker-entrypoint-initdb.d
    networks:
      - sinoair-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - sinoair-network

  # MongoDB文档数据库
  mongodb:
    image: mongo:6
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: admin123
    volumes:
      - mongodb_data:/data/db
    networks:
      - sinoair-network

  # RabbitMQ消息队列
  rabbitmq:
    image: rabbitmq:3-management
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: admin123
    ports:
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - sinoair-network

volumes:
  mysql_data:
  redis_data:
  mongodb_data:
  rabbitmq_data:

networks:
  sinoair-network:
    driver: bridge
```

### 9.2 云原生架构

#### 9.2.1 Kubernetes部署架构
```yaml
# Namespace
apiVersion: v1
kind: Namespace
metadata:
  name: sinoair-agent

---
# ConfigMap
apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
  namespace: sinoair-agent
data:
  application.yml: |
    spring:
      profiles:
        active: k8s
      datasource:
        url: *********************************************
        username: sinoair
        password: sinoair123
      redis:
        host: redis-service
        port: 6379

---
# Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend-deployment
  namespace: sinoair-agent
spec:
  replicas: 3
  selector:
    matchLabels:
      app: backend
  template:
    metadata:
      labels:
        app: backend
    spec:
      containers:
      - name: backend
        image: sinoair/agent-backend:latest
        ports:
        - containerPort: 8081
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "k8s"
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
      volumes:
      - name: config-volume
        configMap:
          name: app-config

---
# Service
apiVersion: v1
kind: Service
metadata:
  name: backend-service
  namespace: sinoair-agent
spec:
  selector:
    app: backend
  ports:
  - protocol: TCP
    port: 8081
    targetPort: 8081
  type: ClusterIP

---
# Ingress
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: app-ingress
  namespace: sinoair-agent
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  rules:
  - host: sinoair-agent.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: backend-service
            port:
              number: 8081
```

### 9.3 监控架构

#### 9.3.1 监控体系架构
```mermaid
graph TB
    subgraph "数据采集层"
        A1[应用指标]
        A2[系统指标]
        A3[业务指标]
        A4[日志数据]
    end

    subgraph "数据处理层"
        B1[Prometheus]
        B2[Elasticsearch]
        B3[Logstash]
        B4[Fluentd]
    end

    subgraph "存储层"
        C1[时序数据库]
        C2[日志存储]
        C3[指标存储]
    end

    subgraph "展示层"
        D1[Grafana]
        D2[Kibana]
        D3[自定义Dashboard]
    end

    subgraph "告警层"
        E1[AlertManager]
        E2[钉钉告警]
        E3[邮件告警]
        E4[短信告警]
    end

    A1 --> B1
    A2 --> B1
    A3 --> B1
    A4 --> B3

    B1 --> C1
    B2 --> C2
    B3 --> C2
    B4 --> C2

    C1 --> D1
    C2 --> D2
    C1 --> D3

    B1 --> E1
    E1 --> E2
    E1 --> E3
    E1 --> E4
```

#### 9.3.2 监控指标设计
```yaml
系统指标:
  - CPU使用率
  - 内存使用率
  - 磁盘使用率
  - 网络IO
  - 文件句柄数

应用指标:
  - QPS (每秒请求数)
  - 响应时间
  - 错误率
  - 活跃连接数
  - 线程池状态

业务指标:
  - 文件处理数量
  - Agent调用次数
  - 回填成功率
  - 用户活跃度
  - 系统容量使用率

告警规则:
  - CPU使用率 > 80% 持续5分钟
  - 内存使用率 > 85% 持续3分钟
  - 错误率 > 5% 持续2分钟
  - 响应时间 > 3秒 持续1分钟
  - 回填成功率 < 90% 持续10分钟
```

## 10. 性能架构设计

### 10.1 性能优化策略

#### 10.1.1 应用层优化
```java
@Configuration
@EnableAsync
public class PerformanceConfig {

    // 异步任务线程池
    @Bean("taskExecutor")
    public ThreadPoolTaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(50);
        executor.setQueueCapacity(200);
        executor.setThreadNamePrefix("async-task-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }

    // 文件处理线程池
    @Bean("fileProcessorExecutor")
    public ThreadPoolTaskExecutor fileProcessorExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("file-processor-");
        return executor;
    }
}

// 异步文件处理
@Service
public class AsyncProcessingService {

    @Async("fileProcessorExecutor")
    public CompletableFuture<ProcessingResult> processFileAsync(ProcessingRequest request) {
        try {
            ProcessingResult result = processFile(request);
            return CompletableFuture.completedFuture(result);
        } catch (Exception e) {
            CompletableFuture<ProcessingResult> future = new CompletableFuture<>();
            future.completeExceptionally(e);
            return future;
        }
    }
}
```

#### 10.1.2 数据库优化
```sql
-- 索引优化
CREATE INDEX idx_processing_records_user_created ON processing_records(user_id, created_at);
CREATE INDEX idx_processing_records_agent_status ON processing_records(agent_id, status);
CREATE INDEX idx_fillback_records_status_created ON fillback_records(status, created_at);

-- 分区表设计
CREATE TABLE processing_records_2024 (
    LIKE processing_records INCLUDING ALL
) PARTITION BY RANGE (EXTRACT(YEAR FROM created_at));

CREATE TABLE processing_records_2024_q1 PARTITION OF processing_records_2024
    FOR VALUES FROM ('2024-01-01') TO ('2024-04-01');

-- 读写分离配置
@Configuration
public class DataSourceConfig {

    @Bean
    @Primary
    public DataSource masterDataSource() {
        return DataSourceBuilder.create()
            .url("*****************************************")
            .username("sinoair")
            .password("sinoair123")
            .build();
    }

    @Bean
    public DataSource slaveDataSource() {
        return DataSourceBuilder.create()
            .url("****************************************")
            .username("sinoair")
            .password("sinoair123")
            .build();
    }
}
```

### 10.2 扩展性架构

#### 10.2.1 水平扩展设计
```mermaid
graph TB
    subgraph "负载均衡层"
        A1[Nginx]
        A2[HAProxy]
        A3[云负载均衡]
    end

    subgraph "应用集群"
        B1[App Instance 1]
        B2[App Instance 2]
        B3[App Instance 3]
        B4[App Instance N]
    end

    subgraph "数据库集群"
        C1[Master DB]
        C2[Slave DB 1]
        C3[Slave DB 2]
    end

    subgraph "缓存集群"
        D1[Redis Master]
        D2[Redis Slave 1]
        D3[Redis Slave 2]
    end

    A1 --> B1
    A1 --> B2
    A1 --> B3
    A1 --> B4

    B1 --> C1
    B2 --> C2
    B3 --> C3
    B4 --> C1

    B1 --> D1
    B2 --> D2
    B3 --> D3
    B4 --> D1
```

#### 10.2.2 自动扩缩容配置
```yaml
# Kubernetes HPA配置
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: backend-hpa
  namespace: sinoair-agent
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: backend-deployment
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 60
```

## 11. 总结

### 11.1 架构特点总结

1. **模块化设计**：采用清晰的模块划分，降低系统耦合度
2. **分层架构**：经典的三层架构，职责分明，易于维护
3. **微服务化**：支持服务拆分，便于独立部署和扩展
4. **云原生**：容器化部署，支持Kubernetes编排
5. **高可用**：集群部署，故障转移，保证系统稳定性
6. **高性能**：多级缓存，异步处理，数据库优化
7. **安全性**：多重认证，权限控制，数据加密
8. **可观测性**：完善的监控告警体系
9. **LLM统一访问**：统一的大模型访问接口，支持多种LLM提供商
10. **智能路由**：基于负载均衡和健康检查的智能模型路由

### 11.2 LLM访问设计优势

1. **统一接口**：所有LLM调用使用相同的请求响应格式，降低业务层复杂度
2. **多模型支持**：支持OpenAI、Claude、文心一言、通义千问等多种大模型
3. **智能路由**：根据模型健康状态、负载情况智能选择最优模型
4. **故障转移**：当某个模型不可用时，自动切换到备用模型
5. **缓存机制**：相同请求结果缓存，提升响应速度，降低成本
6. **监控告警**：完善的模型调用监控和异常告警机制
7. **配置灵活**：支持动态配置模型参数和路由策略
8. **扩展性强**：新增模型只需实现适配器接口，无需修改业务代码

### 11.3 技术选型优势

1. **现代化技术栈**：Java 17 + Spring Boot 3，性能和开发效率并重
2. **成熟的生态**：丰富的第三方组件，降低开发成本
3. **企业级特性**：安全、稳定、可扩展，满足企业级应用需求
4. **开发友好**：完善的开发工具链，提升开发效率
5. **LLM集成**：统一的大模型访问层，支持多厂商模型无缝切换

### 11.4 实施建议

1. **分阶段实施**：按模块逐步实施，降低风险
2. **持续集成**：建立CI/CD流水线，提升交付效率
3. **监控先行**：优先建立监控体系，保证系统可观测性
4. **安全优先**：从设计阶段就考虑安全因素
5. **性能测试**：定期进行性能测试，确保系统性能指标
6. **LLM测试**：建立完善的LLM模型测试和评估机制
7. **成本控制**：合理配置模型调用策略，控制LLM使用成本

### 11.5 LLM访问设计总结

本次新增的LLM访问设计实现了以下核心目标：

1. **统一入口**：通过LLMService提供统一的服务入口，屏蔽不同模型的差异
2. **统一格式**：定义了标准的LLMRequest和LLMResponse格式，确保调用方式一致
3. **多模型支持**：通过适配器模式支持多种大模型，每种模型有独立的实现类
4. **智能路由**：根据配置、健康状态、负载情况智能选择最优模型
5. **高可用性**：支持故障转移、重试机制、健康检查等高可用特性
6. **可扩展性**：新增模型只需实现LLMAdapter接口，无需修改现有代码
7. **监控完善**：提供详细的调用统计、性能监控和异常告警

本架构设计为Sinoair Agent系统提供了完整的技术架构指导，特别是LLM访问层的统一设计，确保了系统在多模型环境下的稳定性、可扩展性和易维护性，为系统的成功实施奠定了坚实的基础。
```